import { getProjects } from '@/lib/markdown'
import ProjectGrid from '@/components/ProjectGrid'
import SubtleGradientBackground from '@/components/SubtleGradientBackground'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Projects | Ernst Romelo - Web Development & AI Automation Portfolio',
  description: 'Explore my portfolio of web development, AI automation, and technology projects. See real-world solutions built with modern technologies.',
  openGraph: {
    title: 'Projects | Ernst Romelo',
    description: 'Explore my portfolio of web development, AI automation, and technology projects.',
    type: 'website',
    url: 'https://ernestomelo.com/projects',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Projects | Ernst Romelo',
    description: 'Explore my portfolio of web development, AI automation, and technology projects.',
  }
}

export default async function ProjectsPage() {
  const projects = await getProjects()

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Ernst Romelo Projects",
    "description": "Portfolio of web development and AI automation projects",
    "url": "https://ernestomelo.com/projects",
    "author": {
      "@type": "Person",
      "name": "<PERSON>",
      "url": "https://ernestomelo.com"
    },
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": projects.map((project, index) => ({
        "@type": "CreativeWork",
        "position": index + 1,
        "name": project.title,
        "description": project.description,
        "url": `https://ernestomelo.com/projects/${project.slug}`,
        "image": project.featuredImage,
        "author": {
          "@type": "Person",
          "name": "Ernst Romelo"
        },
        "dateCreated": project.date
      }))
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <SubtleGradientBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-6 animate-fade-in">
            <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-orange-400 dark:from-blue-400 dark:via-purple-400 dark:to-orange-300 bg-clip-text text-transparent">
              My Projects
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
            Explore my portfolio of web development, AI automation, and technology solutions.
            Each project showcases real-world problem-solving with modern technologies.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="mb-20">
          <ProjectGrid projects={projects} />
        </div>
      </div>
    </>
  )
}
