// Setup script to create storage bucket and policies
// Run with: node scripts/setup-storage.js

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function setupStorage() {
  console.log('Setting up Supabase storage...')
  
  // Create admin client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )

  try {
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()
    
    if (listError) {
      console.error('Error listing buckets:', listError)
      return
    }

    const mediaBucket = buckets.find(bucket => bucket.name === 'media')
    
    if (mediaBucket) {
      console.log('✅ Media bucket already exists')
    } else {
      console.log('Creating media bucket...')
      
      // Create bucket
      const { data, error } = await supabase.storage.createBucket('media', {
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      })

      if (error) {
        console.error('Error creating bucket:', error)
        return
      }

      console.log('✅ Media bucket created successfully')
    }

    // Test upload
    console.log('Testing upload...')
    const testContent = `Test file created at ${new Date().toISOString()}`
    const testPath = `test-${Date.now()}.txt`
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('media')
      .upload(testPath, testContent, {
        contentType: 'text/plain'
      })

    if (uploadError) {
      console.error('Upload test failed:', uploadError)
    } else {
      console.log('✅ Upload test successful')
      
      // Clean up test file
      await supabase.storage.from('media').remove([testPath])
      console.log('✅ Test file cleaned up')
    }

    console.log('\n🎉 Storage setup complete!')
    console.log('Next steps:')
    console.log('1. Apply the storage policies from storage_policies.sql')
    console.log('2. Test upload at http://localhost:3001/dashboard/test-upload')

  } catch (error) {
    console.error('Setup failed:', error)
  }
}

setupStorage()
