export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Dashboard
            </h1>
            <p className="text-gray-600">
              Welcome to your dashboard!
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <a
              href="/"
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              View Site
            </a>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          {children}
        </div>
      </div>
    </div>
  )
}
