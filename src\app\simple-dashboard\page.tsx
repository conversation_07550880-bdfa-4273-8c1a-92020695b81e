'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import Link from 'next/link'

export default function SimpleDashboardPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth error:', error)
          window.location.href = '/simple-login'
          return
        }

        if (!session) {
          window.location.href = '/simple-login'
          return
        }

        setUser(session.user)
        setLoading(false)
      } catch (err) {
        console.error('Error checking auth:', err)
        window.location.href = '/simple-login'
      }
    }

    checkAuth()
  }, [])

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      window.location.href = '/simple-login'
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Simple Dashboard (No Flicker)
            </h1>
            <p className="text-gray-600">
              Welcome back, {user?.email}!
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <a
              href="/"
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              View Site
            </a>
            <button
              onClick={handleSignOut}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="space-y-6">
            {/* Welcome Section */}
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                🎉 Authentication Success!
              </h2>
              <p className="text-gray-600">
                You have successfully logged in without any flickering. This dashboard uses direct Supabase calls instead of complex state management.
              </p>
            </div>

            {/* User Info */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-900 mb-2">User Information</h3>
              <div className="text-sm space-y-1">
                <p><strong>Email:</strong> {user?.email}</p>
                <p><strong>User ID:</strong> {user?.id}</p>
                <p><strong>Created:</strong> {new Date(user?.created_at).toLocaleDateString()}</p>
              </div>
            </div>

            {/* Quick Navigation */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              {/* Upload Test */}
              <Link
                href="/dashboard/test-upload"
                className="block bg-blue-600 text-white rounded-lg shadow p-6 hover:bg-blue-700 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2">🧪 Test Upload</h3>
                <p className="text-blue-100">Test file upload functionality</p>
              </Link>

              {/* Media Library */}
              <Link
                href="/dashboard/media"
                className="block bg-purple-600 text-white rounded-lg shadow p-6 hover:bg-purple-700 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2">📁 Media Library</h3>
                <p className="text-purple-100">View and manage uploaded files</p>
              </Link>

              {/* Blog Posts */}
              <Link
                href="/dashboard/posts/new"
                className="block bg-green-600 text-white rounded-lg shadow p-6 hover:bg-green-700 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2">📝 New Blog Post</h3>
                <p className="text-green-100">Create a new blog post with featured image</p>
              </Link>

              {/* Projects */}
              <Link
                href="/dashboard/projects/new"
                className="block bg-orange-600 text-white rounded-lg shadow p-6 hover:bg-orange-700 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2">🚀 New Project</h3>
                <p className="text-orange-100">Add a new project with featured image</p>
              </Link>

              {/* View All Posts */}
              <Link
                href="/dashboard/posts"
                className="block bg-indigo-600 text-white rounded-lg shadow p-6 hover:bg-indigo-700 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2">📖 All Posts</h3>
                <p className="text-indigo-100">Manage existing blog posts</p>
              </Link>

              {/* View All Projects */}
              <Link
                href="/dashboard/projects"
                className="block bg-teal-600 text-white rounded-lg shadow p-6 hover:bg-teal-700 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2">💼 All Projects</h3>
                <p className="text-teal-100">Manage existing projects</p>
              </Link>
            </div>

            {/* Test Links */}
            <div className="border-t pt-6">
              <h3 className="font-semibold text-gray-900 mb-4">Test Other Dashboards</h3>
              <div className="space-y-2">
                <Link href="/dashboard" className="block text-blue-600 hover:underline">
                  → Try Complex Dashboard (with auth provider - may flicker)
                </Link>
                <Link href="/debug-dashboard" className="block text-blue-600 hover:underline">
                  → Debug Dashboard (no auth checks)
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
