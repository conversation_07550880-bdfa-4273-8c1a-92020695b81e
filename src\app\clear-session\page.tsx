'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

export default function ClearSessionPage() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const router = useRouter()

  const clearSession = async () => {
    setLoading(true)
    setMessage('')

    try {
      // Sign out from Supabase
      await supabase.auth.signOut()
      
      // Clear all cookies
      document.cookie.split(";").forEach((c) => {
        const eqPos = c.indexOf("=")
        const name = eqPos > -1 ? c.substr(0, eqPos) : c
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
      })

      // Clear localStorage
      localStorage.clear()
      
      // Clear sessionStorage
      sessionStorage.clear()

      setMessage('✅ Session cleared successfully!')
      
      // Redirect to login after 2 seconds
      setTimeout(() => {
        window.location.href = '/login'
      }, 2000)

    } catch (error: any) {
      setMessage(`❌ Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Clear Session</h1>
        
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            This will clear all authentication data including:
          </p>
          <ul className="text-sm text-yellow-800 mt-2 list-disc list-inside">
            <li>Supabase session</li>
            <li>All cookies</li>
            <li>Local storage</li>
            <li>Session storage</li>
          </ul>
        </div>

        <button
          onClick={clearSession}
          disabled={loading}
          className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50 mb-4"
        >
          {loading ? 'Clearing...' : 'Clear Session & Cookies'}
        </button>

        {message && (
          <div className="mt-4 p-3 bg-gray-100 rounded-md">
            <p className="text-sm">{message}</p>
          </div>
        )}

        <div className="mt-4 text-center space-y-2">
          <a href="/login" className="block text-blue-600 hover:underline">
            → Go to Login Page
          </a>
          <a href="/auth-status" className="block text-blue-600 hover:underline">
            → Check Auth Status
          </a>
        </div>
      </div>
    </div>
  )
}
