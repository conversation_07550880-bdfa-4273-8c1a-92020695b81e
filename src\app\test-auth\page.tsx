'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

export default function TestAuthPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('testpassword123')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [sessionInfo, setSessionInfo] = useState<any>(null)

  const router = useRouter()

  const checkSession = async () => {
    const { data: { session }, error } = await supabase.auth.getSession()
    setSessionInfo({ session: !!session, error, user: session?.user?.email })
    setMessage(`Session check: ${session ? 'Active' : 'None'} - User: ${session?.user?.email || 'None'}`)
  }

  const handleLogin = async () => {
    setLoading(true)
    setMessage('')

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      setMessage(`✅ Login successful! User: ${data.user.email}`)
      
      // Check session after login
      setTimeout(async () => {
        await checkSession()
        // Try to navigate to dashboard
        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 1000)
      }, 1000)

    } catch (error: any) {
      setMessage(`❌ Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    setLoading(true)
    setMessage('')

    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      setMessage('✅ Logout successful!')
      setSessionInfo(null)
    } catch (error: any) {
      setMessage(`❌ Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Authentication Test</h1>
        
        <div className="mb-4 p-3 bg-gray-100 rounded">
          <p><strong>Session Info:</strong></p>
          <pre className="text-xs mt-2">{JSON.stringify(sessionInfo, null, 2)}</pre>
        </div>

        <div className="space-y-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              disabled={loading}
            />
          </div>
        </div>

        <div className="space-y-2 mb-4">
          <button
            onClick={handleLogin}
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>
          
          <button
            onClick={handleLogout}
            disabled={loading}
            className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {loading ? 'Signing out...' : 'Sign Out'}
          </button>
          
          <button
            onClick={checkSession}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
          >
            Check Session
          </button>

          <button
            onClick={() => router.push('/dashboard')}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
          >
            Go to Dashboard
          </button>
        </div>

        {message && (
          <div className="mt-4 p-3 bg-gray-100 rounded-md">
            <p className="text-sm">{message}</p>
          </div>
        )}
      </div>
    </div>
  )
}
