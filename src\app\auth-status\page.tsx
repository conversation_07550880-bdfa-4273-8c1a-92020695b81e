'use client'

import { useAuth } from '@/components/providers/AuthProvider'
import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import Link from 'next/link'

export default function AuthStatusPage() {
  const { user, session, loading, initialized } = useAuth()
  const [directSession, setDirectSession] = useState<any>(null)

  useEffect(() => {
    const checkDirectSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      setDirectSession({ session: !!session, user: session?.user?.email, error })
    }
    checkDirectSession()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Authentication Status</h1>
        
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Auth Provider Status</h3>
            <div className="text-sm space-y-1">
              <p><strong>Initialized:</strong> {initialized ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Loading:</strong> {loading ? '⏳ Yes' : '✅ No'}</p>
              <p><strong>User:</strong> {user ? `✅ ${user.email}` : '❌ None'}</p>
              <p><strong>Session:</strong> {session ? '✅ Active' : '❌ None'}</p>
            </div>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">Direct Supabase Check</h3>
            <div className="text-sm">
              <pre className="bg-green-100 p-2 rounded text-xs overflow-auto">
                {JSON.stringify(directSession, null, 2)}
              </pre>
            </div>
          </div>

          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Navigation Links</h3>
            <div className="space-y-2">
              <Link href="/login" className="block text-blue-600 hover:underline">
                → Go to Login Page
              </Link>
              <Link href="/test-auth" className="block text-blue-600 hover:underline">
                → Go to Test Auth Page
              </Link>
              <Link href="/debug-dashboard" className="block text-blue-600 hover:underline">
                → Go to Debug Dashboard (No Auth Required)
              </Link>
              <Link href="/dashboard" className="block text-blue-600 hover:underline">
                → Try to Access Dashboard (Should redirect if not authenticated)
              </Link>
            </div>
          </div>

          <div className="p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-900 mb-2">Expected Behavior</h3>
            <div className="text-sm space-y-1">
              <p>• If not authenticated: /dashboard → redirects to /login</p>
              <p>• If authenticated: /login → redirects to /dashboard</p>
              <p>• Middleware should handle redirects automatically</p>
              <p>• No 404 errors should occur on valid routes</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
