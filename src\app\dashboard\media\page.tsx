'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

interface UploadedFile {
  id: string
  filename: string
  original_name: string
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by: string
  created_at: string
}

export default function MediaPage() {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [deleting, setDeleting] = useState<string | null>(null)
  const [message, setMessage] = useState('')

  useEffect(() => {
    loadFiles()
  }, [])

  const loadFiles = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('Please log in to view media')
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from('uploaded_files')
        .select('*')
        .eq('uploaded_by', session.user.id)
        .order('created_at', { ascending: false })

      if (error) {
        setMessage(`Error loading files: ${error.message}`)
      } else {
        setFiles(data || [])
        setMessage(`Loaded ${data?.length || 0} files`)
      }
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    setMessage('Uploading...')

    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('Please log in to upload files')
        setUploading(false)
        return
      }

      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: formData
      })

      const result = await response.json()

      if (response.ok) {
        setMessage('Upload successful!')
        loadFiles() // Reload the file list
      } else {
        setMessage(`Upload failed: ${result.error}`)
      }
    } catch (error: any) {
      setMessage(`Upload error: ${error.message}`)
    } finally {
      setUploading(false)
    }
  }

  const getPublicUrl = (filePath: string) => {
    const { data: { publicUrl } } = supabase.storage
      .from('media')
      .getPublicUrl(filePath)
    return publicUrl
  }

  const handleDeleteFile = async (file: UploadedFile) => {
    if (!confirm(`Are you sure you want to delete "${file.original_name}"? This action cannot be undone.`)) {
      return
    }

    setDeleting(file.id)
    setMessage('Deleting file...')

    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('Please log in to delete files')
        setDeleting(null)
        return
      }

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('media')
        .remove([file.file_path])

      if (storageError) {
        throw new Error(`Storage deletion failed: ${storageError.message}`)
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('uploaded_files')
        .delete()
        .eq('id', file.id)
        .eq('uploaded_by', session.user.id) // Extra security check

      if (dbError) {
        throw new Error(`Database deletion failed: ${dbError.message}`)
      }

      // Update local state
      setFiles(files.filter(f => f.id !== file.id))
      setMessage('File deleted successfully')
    } catch (error: any) {
      console.error('Delete error:', error)
      setMessage(`Delete failed: ${error.message}`)
    } finally {
      setDeleting(null)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Media Library</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Upload and manage your media files
        </p>
      </div>

      {/* Upload Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Upload New File
        </h2>
        <div className="flex items-center space-x-4">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          {uploading && (
            <div className="text-blue-600">Uploading...</div>
          )}
        </div>
        {message && (
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{message}</p>
        )}
      </div>

      {/* Files Grid */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Your Files ({files.length})
        </h2>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading files...</p>
          </div>
        ) : files.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No files uploaded yet</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              Upload your first image above
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {files.map((file) => (
              <div key={file.id} className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden relative group">
                <div className="aspect-square bg-gray-100 dark:bg-gray-700">
                  <img
                    src={getPublicUrl(file.file_path)}
                    alt={file.original_name}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  {/* Delete button overlay */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => handleDeleteFile(file)}
                      disabled={deleting === file.id}
                      className="bg-red-600 hover:bg-red-700 text-white p-1.5 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Delete file"
                    >
                      {deleting === file.id ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {file.original_name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {Math.round(file.file_size / 1024)} KB
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(file.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
