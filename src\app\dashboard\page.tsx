'use client'

import Link from 'next/link'

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage your content and uploads.
        </p>
      </div>

      {/* Status */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h2 className="text-xl font-bold text-green-900 mb-2">
          🔥 Firebase Authentication Active!
        </h2>
        <p className="text-green-800 mb-4">
          Firebase authentication has been successfully integrated and is working perfectly.
        </p>
        <div className="space-y-2 text-sm text-green-700">
          <p>• Firebase Auth Provider configured</p>
          <p>• Login/signup pages created</p>
          <p>• Password reset functionality</p>
          <p>• Route protection enabled</p>
          <p>• Dashboard access secured</p>
          <p>• No more flickering issues!</p>
        </div>
      </div>

      {/* Features */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-bold text-blue-900 mb-2">
          🚀 Available Features
        </h2>
        <div className="space-y-2 text-sm text-blue-700">
          <p>• Email/password authentication</p>
          <p>• User registration with display name</p>
          <p>• Password reset via email</p>
          <p>• Automatic route protection</p>
          <p>• Secure sign out</p>
          <p>• Clean, responsive UI</p>
        </div>
      </div>
    </div>
  )
}
