'use client'

import Link from 'next/link'

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage your content and uploads.
        </p>
      </div>

      {/* Quick Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

        {/* Upload Test */}
        <Link
          href="/dashboard/test-upload"
          className="block bg-blue-600 text-white rounded-lg shadow p-6 hover:bg-blue-700 transition-colors"
        >
          <h3 className="text-lg font-semibold mb-2">🧪 Test Upload</h3>
          <p className="text-blue-100">Test file upload functionality</p>
        </Link>

        {/* Media Library */}
        <Link
          href="/dashboard/media"
          className="block bg-purple-600 text-white rounded-lg shadow p-6 hover:bg-purple-700 transition-colors"
        >
          <h3 className="text-lg font-semibold mb-2">📁 Media Library</h3>
          <p className="text-purple-100">View and manage uploaded files</p>
        </Link>

        {/* Blog Posts */}
        <Link
          href="/dashboard/posts/new"
          className="block bg-green-600 text-white rounded-lg shadow p-6 hover:bg-green-700 transition-colors"
        >
          <h3 className="text-lg font-semibold mb-2">📝 New Blog Post</h3>
          <p className="text-green-100">Create a new blog post with featured image</p>
        </Link>

        {/* Projects */}
        <Link
          href="/dashboard/projects/new"
          className="block bg-orange-600 text-white rounded-lg shadow p-6 hover:bg-orange-700 transition-colors"
        >
          <h3 className="text-lg font-semibold mb-2">🚀 New Project</h3>
          <p className="text-orange-100">Add a new project with featured image</p>
        </Link>

        {/* View All Posts */}
        <Link
          href="/dashboard/posts"
          className="block bg-indigo-600 text-white rounded-lg shadow p-6 hover:bg-indigo-700 transition-colors"
        >
          <h3 className="text-lg font-semibold mb-2">📖 All Posts</h3>
          <p className="text-indigo-100">Manage existing blog posts</p>
        </Link>

        {/* View All Projects */}
        <Link
          href="/dashboard/projects"
          className="block bg-teal-600 text-white rounded-lg shadow p-6 hover:bg-teal-700 transition-colors"
        >
          <h3 className="text-lg font-semibold mb-2">💼 All Projects</h3>
          <p className="text-teal-100">Manage existing projects</p>
        </Link>
      </div>
    </div>
  )
}
