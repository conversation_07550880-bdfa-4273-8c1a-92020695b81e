import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getBlogPost, getBlogPosts, getAdjacentPosts } from '@/lib/markdown'
import TableOfContents from '@/components/TableOfContents'
import SocialSidebar from '@/components/SocialSidebar'
import ArticleNavigation from '@/components/ArticleNavigation'
import RelatedPosts from '@/components/RelatedPosts'
import NewsletterSignup from '@/components/NewsletterSignup'
import ViewCounter from '@/components/ViewCounter'
import MobileSocialShare from '@/components/MobileSocialShare'
import OptimizedImage from '@/components/OptimizedImage'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateStaticParams() {
  const posts = await getBlogPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export async function generateMetadata({ params }: BlogPostPageProps) {
  const post = await getBlogPost(params.slug)

  if (!post) {
    return {
      title: 'Post Not Found | <PERSON>',
      description: 'The requested blog post could not be found.',
    }
  }

  return {
    title: `${post.title} | <PERSON>`,
    description: post.excerpt,
    keywords: post.tags?.join(', '),
    authors: [{ name: post.author || '<PERSON>lo' }],
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.date,
      authors: [post.author || 'Ernst Romelo'],
      images: [
        {
          url: post.featuredImage,
          width: 1200,
          height: 630,
          alt: post.title,
        }
      ],
      url: `https://ernestomelo.com/blog/${params.slug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      images: [post.featuredImage],
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const post = await getBlogPost(params.slug)

  if (!post) {
    notFound()
  }

  // Get adjacent posts and all posts for related posts
  const { previousPost, nextPost } = await getAdjacentPosts(params.slug)
  const allPosts = await getBlogPosts()

  // Add IDs to headings for table of contents (H2-H4 only, skip H1)
  const contentWithIds = post.content.replace(
    /<h([2-4])([^>]*)>([^<]*)<\/h[2-4]>/g,
    (_, level, attrs, text) => {
      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
      return `<h${level}${attrs} id="${id}">${text}</h${level}>`
    }
  )

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": post.title,
    "description": post.excerpt,
    "image": {
      "@type": "ImageObject",
      "url": post.featuredImage,
      "width": 1200,
      "height": 630
    },
    "author": {
      "@type": "Person",
      "name": post.author || "Ernst Romelo",
      "url": "https://ernestomelo.com"
    },
    "publisher": {
      "@type": "Person",
      "name": "Ernst Romelo",
      "url": "https://ernestomelo.com"
    },
    "datePublished": post.date,
    "dateModified": post.date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://ernestomelo.com/blog/${params.slug}`
    },
    "url": `https://ernestomelo.com/blog/${params.slug}`,
    "keywords": post.tags?.join(', '),
    "articleSection": post.categories?.join(', '),
    "wordCount": post.content.split(/\s+/).length,
    "timeRequired": `PT${post.readTime}M`
  }

  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back to Blog */}
        <Link
          href="/"
          className="inline-flex items-center text-primary-600 dark:text-primary-300 hover:text-primary-400 dark:hover:text-primary-200 mb-8 transition-colors duration-300"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Blog
        </Link>

        {/* Featured Image - Square crop on mobile, full size on desktop */}
        <div className="relative mb-12 -mx-4 sm:-mx-6 lg:-mx-8">
          <OptimizedImage
            src={post.featuredImage}
            alt={post.title}
            width={1200}
            height={630}
            className="w-full h-80 sm:h-96 md:h-auto object-cover object-center rounded-xl"
            priority
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
          {/* Social Sidebar - Hidden on mobile, shown on desktop */}
          <div className="lg:col-span-1 hidden lg:block">
            <SocialSidebar
              readTime={post.readTime}
              title={post.title}
              url={`/blog/${post.slug}`}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-8 col-span-1">

            {/* Article Header */}
            <header className="mb-8">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4 leading-tight">
                {post.title}
              </h1>

              <div className="flex items-center text-gray-600 dark:text-gray-300 mb-6">
                <time dateTime={post.date}>
                  {new Date(post.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
                <span className="mx-3">•</span>
                <span>{post.readTime} min read</span>
                <span className="mx-3">•</span>
                <ViewCounter slug={post.slug} />
              </div>



              {post.excerpt && (
                <p className="text-xl text-gray-700 dark:text-gray-200 leading-relaxed">
                  {post.excerpt}
                </p>
              )}
            </header>

            {/* Article Content */}
            <article
              className="blog-content prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: contentWithIds }}
            />

            {/* Mobile Social Sharing - Right after content */}
            <MobileSocialShare title={post.title} slug={post.slug} />

            {/* Article Navigation */}
            <ArticleNavigation
              previousArticle={previousPost ? { slug: previousPost.slug, title: previousPost.title } : undefined}
              nextArticle={nextPost ? { slug: nextPost.slug, title: nextPost.title } : undefined}
            />

            {/* Related Posts */}
            <RelatedPosts posts={allPosts} currentPostSlug={post.slug} />

            {/* Newsletter Signup */}
            <NewsletterSignup />
          </div>

          {/* Table of Contents Sidebar */}
          <div className="lg:col-span-3">
            <TableOfContents content={contentWithIds} />
          </div>
        </div>

        {/* Add bottom margin for footer */}
        <div className="mb-20"></div>
      </div>
    </div>
  )
}
