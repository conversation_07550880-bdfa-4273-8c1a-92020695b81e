import { supabase } from '@/lib/supabase'
import { BlogPost, Project } from '@/types'

// Process markdown content to HTML
export async function processMarkdownContent(content: string): Promise<string> {
  // If content is empty or null, return empty string
  if (!content || content.trim() === '') {
    return ''
  }

  // For now, return content with basic formatting to avoid server-side remark issues
  // TODO: Fix remark processing for server-side rendering
  return content
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
}

// Convert Supabase blog post to frontend BlogPost format
async function convertSupabaseBlogPost(data: any): Promise<BlogPost> {
  const processedContent = await processMarkdownContent(data.content || '')

  return {
    slug: data.slug,
    title: data.title,
    excerpt: data.excerpt || '',
    date: data.published_at || data.created_at, // Use published_at if available, fallback to created_at
    featuredImage: data.featured_image || '/images/blog/default.png',
    content: processedContent,
    readTime: data.reading_time || 5,
    tags: data.tags || [],
    author: '<PERSON>lo',
    categories: [],
  }
}

// Convert Supabase project to frontend Project format
async function convertSupabaseProject(data: any): Promise<Project> {
  const processedContent = await processMarkdownContent(data.content || '')

  return {
    slug: data.slug,
    title: data.title,
    description: data.description || '',
    featuredImage: data.featured_image || '/images/projects/default.jpg',
    images: [],
    technologies: data.tech_stack || [],
    liveUrl: data.project_url,
    githubUrl: data.github_url,
    content: processedContent,
    date: data.published_at || data.created_at, // Use published_at if available, fallback to created_at
    category: 'Web Development',
    client: data.client,
    industry: data.industry,
    challenge: data.challenge,
    solution: data.solution,
    strategy: undefined,
  }
}

// Get all published blog posts from Supabase
export async function getBlogPostsFromSupabase(): Promise<BlogPost[]> {
  try {
    const now = new Date().toISOString()

    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('published', true)
      .lte('published_at', now) // Only show posts where published_at is in the past or now
      .order('published_at', { ascending: false }) // Order by published_at instead of created_at

    if (error) {
      console.error('Error fetching blog posts:', error)
      return []
    }

    return Promise.all(data.map(convertSupabaseBlogPost))
  } catch (error) {
    console.error('Error in getBlogPostsFromSupabase:', error)
    return []
  }
}

// Get a single blog post by slug from Supabase
export async function getBlogPostFromSupabase(slug: string): Promise<BlogPost | null> {
  try {
    const now = new Date().toISOString()

    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .lte('published_at', now) // Only show if published_at is in the past or now
      .single()

    if (error) {
      console.error('Error fetching blog post:', error)
      return null
    }

    return await convertSupabaseBlogPost(data)
  } catch (error) {
    console.error('Error in getBlogPostFromSupabase:', error)
    return null
  }
}

// Get all published projects from Supabase
export async function getProjectsFromSupabase(): Promise<Project[]> {
  try {
    const now = new Date().toISOString()

    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('published', true)
      .lte('published_at', now) // Only show projects where published_at is in the past or now
      .order('published_at', { ascending: false }) // Order by published_at instead of created_at

    if (error) {
      console.error('Error fetching projects:', error)
      return []
    }

    return Promise.all(data.map(convertSupabaseProject))
  } catch (error) {
    console.error('Error in getProjectsFromSupabase:', error)
    return []
  }
}

// Get a single project by slug from Supabase
export async function getProjectFromSupabase(slug: string): Promise<Project | null> {
  try {
    const now = new Date().toISOString()

    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .lte('published_at', now) // Only show if published_at is in the past or now
      .single()

    if (error) {
      console.error('Error fetching project:', error)
      return null
    }

    return await convertSupabaseProject(data)
  } catch (error) {
    console.error('Error in getProjectFromSupabase:', error)
    return null
  }
}

// Get adjacent blog posts for navigation
export async function getAdjacentPostsFromSupabase(currentSlug: string): Promise<{
  previousPost: BlogPost | null;
  nextPost: BlogPost | null;
}> {
  try {
    const allPosts = await getBlogPostsFromSupabase()
    const currentIndex = allPosts.findIndex(post => post.slug === currentSlug)

    return {
      previousPost: currentIndex > 0 ? allPosts[currentIndex - 1] : null,
      nextPost: currentIndex < allPosts.length - 1 ? allPosts[currentIndex + 1] : null,
    }
  } catch (error) {
    console.error('Error in getAdjacentPostsFromSupabase:', error)
    return {
      previousPost: null,
      nextPost: null,
    }
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    // Try to get posts from Supabase first
    const supabasePosts = await getBlogPostsFromSupabase()

    if (supabasePosts.length > 0) {
      return supabasePosts
    }

    // Only try markdown fallback on server side
    if (typeof window === 'undefined') {
      try {
        const { getBlogPosts: getMarkdownPosts } = await import('./markdown')
        return await getMarkdownPosts()
      } catch (markdownError) {
        console.error('Error loading markdown posts:', markdownError)
      }
    }

    return []
  } catch (error) {
    console.error('Error in hybrid getBlogPosts:', error)
    return []
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    // Try to get post from Supabase first
    const supabasePost = await getBlogPostFromSupabase(slug)

    if (supabasePost) {
      return supabasePost
    }

    // Only try markdown fallback on server side
    if (typeof window === 'undefined') {
      try {
        const { getBlogPost: getMarkdownPost } = await import('./markdown')
        return await getMarkdownPost(slug)
      } catch (markdownError) {
        console.error('Error loading markdown post:', markdownError)
      }
    }

    return null
  } catch (error) {
    console.error('Error in hybrid getBlogPost:', error)
    return null
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getProjects(): Promise<Project[]> {
  try {
    // Try to get projects from Supabase first
    const supabaseProjects = await getProjectsFromSupabase()

    if (supabaseProjects.length > 0) {
      return supabaseProjects
    }

    // Only try markdown fallback on server side
    if (typeof window === 'undefined') {
      try {
        const { getProjects: getMarkdownProjects } = await import('./markdown')
        return await getMarkdownProjects()
      } catch (markdownError) {
        console.error('Error loading markdown projects:', markdownError)
      }
    }

    return []
  } catch (error) {
    console.error('Error in hybrid getProjects:', error)
    return []
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getProject(slug: string): Promise<Project | null> {
  try {
    // Try to get project from Supabase first
    const supabaseProject = await getProjectFromSupabase(slug)

    if (supabaseProject) {
      return supabaseProject
    }

    // Only try markdown fallback on server side
    if (typeof window === 'undefined') {
      try {
        const { getProject: getMarkdownProject } = await import('./markdown')
        return await getMarkdownProject(slug)
      } catch (markdownError) {
        console.error('Error loading markdown project:', markdownError)
      }
    }

    return null
  } catch (error) {
    console.error('Error in hybrid getProject:', error)
    return null
  }
}

// Hybrid function for adjacent posts
export async function getAdjacentPosts(currentSlug: string): Promise<{
  previousPost: BlogPost | null;
  nextPost: BlogPost | null;
}> {
  try {
    // Try Supabase first
    const supabasePosts = await getBlogPostsFromSupabase()

    if (supabasePosts.length > 0) {
      return await getAdjacentPostsFromSupabase(currentSlug)
    }

    // Only try markdown fallback on server side
    if (typeof window === 'undefined') {
      try {
        const { getAdjacentPosts: getMarkdownAdjacentPosts } = await import('./markdown')
        return await getMarkdownAdjacentPosts(currentSlug)
      } catch (markdownError) {
        console.error('Error loading markdown adjacent posts:', markdownError)
      }
    }

    return {
      previousPost: null,
      nextPost: null,
    }
  } catch (error) {
    console.error('Error in hybrid getAdjacentPosts:', error)
    return {
      previousPost: null,
      nextPost: null,
    }
  }
}
