'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestCredentialsPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testCredentials = async () => {
    setLoading(true)
    setResult(null)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123',
      })

      if (error) {
        setResult({ success: false, error: error.message })
      } else {
        setResult({ 
          success: true, 
          user: data.user?.email,
          session: !!data.session 
        })
        
        // Sign out immediately after test
        await supabase.auth.signOut()
      }
    } catch (error: any) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Test Credentials</h1>
        
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            This will test the credentials:
          </p>
          <div className="bg-gray-100 p-3 rounded text-sm">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> testpassword123</p>
          </div>
        </div>

        <button
          onClick={testCredentials}
          disabled={loading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 mb-4"
        >
          {loading ? 'Testing...' : 'Test Credentials'}
        </button>

        {result && (
          <div className={`p-4 rounded-md ${result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
            <h3 className="font-semibold mb-2">
              {result.success ? '✅ Success' : '❌ Failed'}
            </h3>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-4 text-center">
          <a href="/login" className="text-blue-600 hover:underline">
            → Go to Login Page
          </a>
        </div>
      </div>
    </div>
  )
}
