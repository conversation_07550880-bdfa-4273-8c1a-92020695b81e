'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'
import { supabase } from '@/lib/supabase'
import { generateSlug } from '@/lib/utils'
import FeaturedImageSelector from '@/components/FeaturedImageSelector'
import ProjectPreview from '@/components/ProjectPreview'

interface EditProjectPageProps {
  params: {
    id: string
  }
}

export default function EditProjectPage({ params }: EditProjectPageProps) {
  const { user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [showPreview, setShowPreview] = useState(false)

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    content: '',
    featuredImage: '',
    project_url: '',
    github_url: '',
    tech_stack: '',
    tags: '',
    client: '',
    industry: '',
    challenge: '',
    solution: '',
    published: false,
  })

  useEffect(() => {
    if (user) {
      loadProject()
    }

    // Listen for preview close event
    const handleClosePreview = () => setShowPreview(false)
    window.addEventListener('close-preview', handleClosePreview)

    return () => {
      window.removeEventListener('close-preview', handleClosePreview)
    }
  }, [user, params.id])

  const loadProject = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', params.id)
        .eq('author_id', user?.id)
        .single()

      if (error) throw error

      setFormData({
        title: data.title,
        slug: data.slug,
        description: data.description || '',
        content: data.content,
        featuredImage: data.featured_image || '',
        project_url: data.project_url || '',
        github_url: data.github_url || '',
        tech_stack: data.tech_stack ? data.tech_stack.join(', ') : '',
        tags: data.tags ? data.tags.join(', ') : '',
        client: data.client || '',
        industry: data.industry || '',
        challenge: data.challenge || '',
        solution: data.solution || '',
        published: data.published,
      })
    } catch (error: any) {
      console.error('Error loading project:', error)
      setError('Failed to load project')
    } finally {
      setLoading(false)
    }
  }

  const handleTitleChange = (title: string) => {
    setFormData({
      ...formData,
      title,
      slug: generateSlug(title),
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setError('')

    try {
      if (!formData.title.trim()) {
        throw new Error('Title is required')
      }

      if (!formData.content.trim()) {
        throw new Error('Content is required')
      }

      const techStackArray = formData.tech_stack
        .split(',')
        .map(tech => tech.trim())
        .filter(tech => tech.length > 0)

      const tagsArray = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)

      const { error } = await supabase
        .from('projects')
        .update({
          title: formData.title.trim(),
          slug: formData.slug || generateSlug(formData.title),
          description: formData.description.trim(),
          content: formData.content.trim(),
          featured_image: formData.featuredImage.trim() || null,
          project_url: formData.project_url.trim() || null,
          github_url: formData.github_url.trim() || null,
          tech_stack: techStackArray.length > 0 ? techStackArray : null,
          tags: tagsArray.length > 0 ? tagsArray : null,
          client: formData.client.trim() || null,
          industry: formData.industry.trim() || null,
          challenge: formData.challenge.trim() || null,
          solution: formData.solution.trim() || null,
          published: formData.published,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.id)
        .eq('author_id', user?.id)

      if (error) throw error

      router.push('/dashboard/projects')
    } catch (error: any) {
      console.error('Error updating project:', error)
      setError(error.message)
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Project</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Update your project details and content.
          </p>
        </div>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded-md">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Project Title *
            </label>
            <input
              type="text"
              id="title"
              value={formData.title}
              onChange={(e) => handleTitleChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter project title"
              required
            />
          </div>

          {/* Slug */}
          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              URL Slug
            </label>
            <input
              type="text"
              id="slug"
              value={formData.slug}
              onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="url-friendly-slug"
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Short Description
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Brief description of the project"
          />
        </div>

        {/* Featured Image */}
        <FeaturedImageSelector
          value={formData.featuredImage}
          onChange={(url) => setFormData({ ...formData, featuredImage: url })}
        />

        {/* Content */}
        <div>
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Project Details *
          </label>
          <textarea
            id="content"
            value={formData.content}
            onChange={(e) => setFormData({ ...formData, content: e.target.value })}
            rows={12}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Detailed description of the project, features, challenges, etc."
            required
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Project URL */}
          <div>
            <label htmlFor="project_url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Live Project URL
            </label>
            <input
              type="url"
              id="project_url"
              value={formData.project_url}
              onChange={(e) => setFormData({ ...formData, project_url: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="https://example.com"
            />
          </div>

          {/* GitHub URL */}
          <div>
            <label htmlFor="github_url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              GitHub Repository
            </label>
            <input
              type="url"
              id="github_url"
              value={formData.github_url}
              onChange={(e) => setFormData({ ...formData, github_url: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="https://github.com/username/repo"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Tech Stack */}
          <div>
            <label htmlFor="tech_stack" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tech Stack
            </label>
            <input
              type="text"
              id="tech_stack"
              value={formData.tech_stack}
              onChange={(e) => setFormData({ ...formData, tech_stack: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="React, Node.js, PostgreSQL"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Separate technologies with commas
            </p>
          </div>

          {/* Tags */}
          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tags
            </label>
            <input
              type="text"
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="web app, e-commerce, portfolio"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Separate tags with commas
            </p>
          </div>
        </div>

        {/* Project Metadata */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Client */}
          <div>
            <label htmlFor="client" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Client
            </label>
            <input
              type="text"
              id="client"
              value={formData.client}
              onChange={(e) => setFormData({ ...formData, client: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Client name or company"
            />
          </div>

          {/* Industry */}
          <div>
            <label htmlFor="industry" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Industry
            </label>
            <input
              type="text"
              id="industry"
              value={formData.industry}
              onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="e.g., E-commerce, Healthcare, Finance"
            />
          </div>
        </div>

        {/* Challenge */}
        <div>
          <label htmlFor="challenge" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            The Challenge
          </label>
          <textarea
            id="challenge"
            value={formData.challenge}
            onChange={(e) => setFormData({ ...formData, challenge: e.target.value })}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Describe the main challenge or problem this project solved..."
          />
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Explain the key challenges faced in this project.
          </p>
        </div>

        {/* Solution */}
        <div>
          <label htmlFor="solution" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            The Solution
          </label>
          <textarea
            id="solution"
            value={formData.solution}
            onChange={(e) => setFormData({ ...formData, solution: e.target.value })}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Describe how you solved the challenge and what approach you took..."
          />
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Explain the solution and approach used to solve the challenge.
          </p>
        </div>

        {/* Published */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="published"
            checked={formData.published}
            onChange={(e) => setFormData({ ...formData, published: e.target.checked })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="published" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Publish this project
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => setShowPreview(true)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Preview
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {saving ? 'Updating...' : formData.published ? 'Update & Publish' : 'Update Draft'}
          </button>
        </div>
      </form>

      {/* Preview Modal */}
      {showPreview && (
        <ProjectPreview
          title={formData.title}
          description={formData.description}
          content={formData.content}
          featuredImage={formData.featuredImage}
          technologies={formData.tech_stack.split(',').map(tech => tech.trim()).filter(tech => tech.length > 0)}
          projectUrl={formData.project_url}
          githubUrl={formData.github_url}
          client={formData.client}
          industry={formData.industry}
          challenge={formData.challenge}
          solution={formData.solution}
          publishedAt={new Date().toISOString()}
        />
      )}
    </div>
  )
}
